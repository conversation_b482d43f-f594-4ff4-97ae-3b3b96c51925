services:
  sentry:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
      args:
        IMAGE: ${IMAGE}
        VERSION: ${VERSION}
    volumes:
      - ..:/workspace/sentry:cached
      - sentry_e2e_logs:/workspace/sentry/log
      - sentry_ruby_gems:/workspace/gems
    command: sleep infinity
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis

  sentry-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
      args:
        IMAGE: ${IMAGE}
        VERSION: ${VERSION}
    volumes:
      - ..:/workspace/sentry:cached
      - sentry_e2e_logs:/workspace/sentry/log
      - sentry_ruby_gems:/workspace/gems
    working_dir: /workspace/sentry
    entrypoint: .devcontainer/entrypoint-sentry-test.sh
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis
      - sentry-rails-mini
      - sentry-svelte-mini

  sentry-rails-mini:
    build:
      context: .
      dockerfile: Dockerfile
      target: rails-mini
      args:
        IMAGE: ${IMAGE}
        VERSION: ${VERSION}
    working_dir: /workspace/sentry/spec/apps/rails-mini
    volumes:
      - ..:/workspace/sentry:cached
      - sentry_e2e_logs:/workspace/sentry/log
      - sentry_ruby_gems:/workspace/gems
    ports:
      - "${SENTRY_E2E_RAILS_APP_PORT:-4000}:4000"
    command: ruby app.rb
    profiles:
      - e2e
    env_file: [".env"]

  sentry-svelte-mini:
    build:
      context: .
      dockerfile: Dockerfile
      target: svelte-mini
      args:
        IMAGE: ${IMAGE}
        VERSION: ${VERSION}
    working_dir: /workspace/sentry/spec/apps/svelte-mini
    volumes:
      - ..:/workspace/sentry:cached
      - sentry_node_modules:/workspace/sentry/spec/apps/svelte-mini/node_modules
    ports:
      - "${SENTRY_E2E_SVELTE_APP_PORT:-4001}:4001"
    command: sh -c "cd /workspace/sentry/spec/apps/svelte-mini && npm run dev -- --host 0.0.0.0"
    environment:
      SENTRY_E2E_RAILS_APP_URL: "http://sentry-rails-mini:${SENTRY_E2E_RAILS_APP_PORT:-4000}"
    profiles:
      - e2e
    env_file: [".env"]

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"

volumes:
  sentry_e2e_logs:
  sentry_ruby_gems:
  sentry_node_modules:
